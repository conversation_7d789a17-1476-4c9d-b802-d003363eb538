import math, numpy as np
from config import Config

class RulePolicy:
    def __init__(self, env):
        self.env = env
        self.n_boat  = env.num_white_boats
        self.n_drone = env.num_white_drones
        # — 扫描用状态缓存
        self.scan_phase = 0         # 0: 出击   1: 锯齿向右   2: 锯齿向左
        self.scan_count = 0

    # ----------- 主入口 -----------
    def __call__(self, obs):
        act = np.zeros(self.env.action_space.shape, dtype=np.float32)

        # ========== 1. 白方无人艇 ==========
        for i, boat in enumerate(self.env.white_boats):
            idx = 2*i
            if not boat.active or boat.frozen_time > 0:
                act[idx:idx+2] = 0         # 保持
                continue
            vis = boat.detect(self.env.black_boats)
            if vis:
                tgt = min(vis, key=lambda b: np.linalg.norm(b.pos-boat.pos)).pos
            else:
                tgt = (Config.BREAKTHROUGH_LINE[0] + Config.BREAKTHROUGH_LINE[1]) / 2
            vec = tgt - boat.pos
            act[idx]   = 1.0               # 满速
            act[idx+1] = math.atan2(vec[1], vec[0]) / math.pi

        # ========== 2. 白方无人机 ==========
        base = 2*self.n_boat
        for j, drone in enumerate(self.env.white_drones):
            idx = base + 2*j

            # 2-a 起飞逻辑 -------------------------------------------------
            if drone.on_boat:
                # 连续几步给 +1，直到 off-deck
                act[idx]   = 1.0 if drone.energy >= 0.5 else -1.0
                act[idx+1] = 0.0
                continue

            # 2-b 返航（能量 < 20 %） -------------------------------------
            if drone.energy < 0.2:
                home = min(self.env.white_boats,
                           key=lambda b: np.linalg.norm(drone.pos-b.pos))
                vec = home.pos - drone.pos
                act[idx]   = 1.0
                act[idx+1] = math.atan2(vec[1], vec[0]) / math.pi
                continue

            # 2-c 探测 / 跟踪 ---------------------------------------------
            vis = drone.detect(self.env.black_boats)     # 60° 扇形
            if vis:                                      # → 跟踪模式
                tgt = min(vis, key=lambda b: np.linalg.norm(b.pos-drone.pos)).pos
                vec = tgt - drone.pos
                act[idx]   = 0.0                         # 交给 update_tracking
                act[idx+1] = math.atan2(vec[1], vec[0]) / math.pi
                # 重置扫描器
                self.scan_phase, self.scan_count = 0, 0
            else:
                # ---- d. 巡逻扇区站岗 ----
                patrol_vec = self._search_pattern(drone)        # ndarray 长度 2
                act[idx:idx+2] = patrol_vec                     # ★ 只写 2 个槽位
                self.scan_phase, self.scan_count = self._update_scan()


        return act

    # ----------- 搜索模式（无人机专用） -----------
    def _search_pattern(self, drone):
        """返回 (速度_norm, 归一化_heading)"""
        mother = self.env.white_boats[0]
        if self.scan_phase == 0:
            # 出击到母艇正北 2 km
            tgt = mother.pos + np.array([0, 2])
            if np.linalg.norm(drone.pos - tgt) < 0.1:
                self.scan_phase = 1
                self.scan_count = 0
            vec = tgt - drone.pos
            speed = 0.5                           # ≈ (min+max)/2
            head  = math.atan2(vec[1], vec[0])
        else:
            # 锯齿扫描：每 3 步切换左右 45°
            base_head = math.pi/2                 # 朝北
            offset = math.radians(45)
            if self.scan_phase == 1:
                head = base_head + offset
            else:
                head = base_head - offset
            speed = 0.2                           # 0.1 km/s ≈ 巡航
        return np.array([speed*2-1, head/math.pi], dtype=np.float32)

    def _update_scan(self):
        if self.scan_phase == 0:
            return self.scan_phase, self.scan_count
        self.scan_count += 1
        if self.scan_count >= 3:
            self.scan_phase = 1 if self.scan_phase == 2 else 2
            self.scan_count = 0
        return self.scan_phase, self.scan_count
