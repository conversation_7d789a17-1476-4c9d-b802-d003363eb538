# main.py
import time
import pygame

from env import UnmannedClusterEnv          # 你的仿真环境
from rule_policy import RulePolicy          # 只用探测结果的规则策略


def main():
    # ---------- 0. 初始化 ----------
    pygame.init()                           # 若 env 内部已 init，可删掉
    env = UnmannedClusterEnv(n_white_boat=5,
                         n_white_drone=5,
                         n_black_boat=5)
    obs = env.reset()
    policy = RulePolicy(env)

    # ---------- 1. 开始新回合 ----------
   
    done   = False
    step_n = 0

    # ---------- 2. 主循环 ----------
    while not done:
        # 2.1 处理窗口关闭事件（防止卡死）
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                env.close()
                return

        # 2.2 由策略生成动作向量
        action = policy(obs)                # ndarray，shape = env.action_space.shape

        # 2.3 环境步进
        obs, reward, done, info = env.step(action)

        # 2.4 渲染与节流
        env.render()
        time.sleep(0.03)                    # ~30 FPS，可按需调小

        step_n += 1

    # ---------- 3. 回合结束 ----------
    print(f"Episode finished in {step_n} steps — total reward: {reward:.2f}")
    env.close()


if __name__ == "__main__":
    main()
