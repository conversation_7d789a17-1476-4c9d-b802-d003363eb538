import gym
from gym import spaces
import numpy as np
import pygame
import math
from config import Config
from entity import Boat, Drone
from typing import List
from matplotlib.path import Path

# 环境类
class UnmannedClusterEnv(gym.Env):
    metadata = {'render.modes': ['human']}

    def __init__(self,
             n_white_boat : int = 1,
             n_white_drone: int = 1,
             n_black_boat : int = 2):
        super().__init__()
        self.num_white_boats  = n_white_boat
        self.num_white_drones = n_white_drone
        self.num_black_boats  = n_black_boat

        # 动作空间：为每个白方艇/无人机指定目标速度和朝向（简化）
        # 每个艇/无人机： [target_speed_norm, target_heading]
        action_dim = 2 * (self.num_white_boats + self.num_white_drones)
        self.action_space = spaces.Box(low=-1, high=1, shape=(action_dim,), dtype=np.float32)

        # 观测空间：所有实体位置、速度、朝向、锁定状态等（简化）
        obs_dim = 6 * (self.num_white_boats + self.num_white_drones + self.num_black_boats)  # pos_x, pos_y, vel, heading, energy/lock, etc.
        self.observation_space = spaces.Box(low=-np.inf, high=np.inf, shape=(obs_dim,), dtype=np.float32)

        self.white_boats: List[Boat] = []
        self.white_drones: List[Drone] = []
        self.black_boats: List[Boat] = []
        self.dt = 100  # 时间步，秒
        self.time = 0
        self.intercepted = 0
        self.lock_success = 0
        self.be_locked = 0
        self.collisions = 0
        self.total_black_intercepted = 0
        self.total_lock_success = 0
        self.total_be_locked = 0
        self.total_collisions = 0
        self.random_target_pos = []
        # Pygame
        self.screen = None

        self.reset()

    def reset(self):
        self.white_boats = []
        self.white_drones = []
        self.black_boats = []
        self.time = 0
        self.intercepted = 0
        self.lock_success = 0
        self.be_locked = 0
        self.collisions = 0
        self.random_target_pos = []
        # 初始化白方无人艇
        interval = 5
        start_x = Config.A7A8_MID[0] - (self.num_white_boats - 1) * interval / 2
        for i in range(self.num_white_boats):
            pos = np.array([start_x + i * interval, Config.A7A8_MID[1]])
            boat = Boat(pos, heading=math.pi / 2)
            # boat.detect_range = 20
            # boat.lock_range = 40
            boat.detect_range = 60
            boat.lock_range = 60
            boat.side = 'white'
            self.white_boats.append(boat)

        # 初始化白方无人机（假设初始在艇上）
        for i in range(self.num_white_drones):
            pos = self.white_boats[i].pos.copy()
            drone = Drone(pos, heading=math.pi / 2)
            drone.on_boat = self.white_boats[i]
            drone.charging = True
            self.white_drones.append(drone)

        # 初始化黑方无人艇（随机在梯形内，简单随机策略）
        for _ in range(self.num_black_boats):
            # 随机位置在A2A3A4A5（简化均匀随机）
            while True:
                black_area_path = Path(Config.BLACK_INIT_AREA)
                pos = np.random.uniform(low=np.min(Config.BLACK_INIT_AREA, axis=0), high=np.max(Config.BLACK_INIT_AREA, axis=0))
                if black_area_path.contains_point(pos):
                    break
            boat = Boat(pos, vel=0.01, heading=math.pi / 2)
            boat.detect_range = 30
            boat.side = 'black'
            self.black_boats.append(boat)  # 向下

        # 初始化和白色无人艇数量一样多的随机点，保证在地图内
        for _ in range(self.num_white_boats):
            # 随机位置在config的ALL_AREA（简化均匀随机）
            pos = np.random.uniform(low=np.min(Config.ALL_AREA, axis=0), high=np.max(Config.ALL_AREA, axis=0))
            # 判断pos是否在梯形内
            while True:
                all_area_path = Path(Config.ALL_AREA)
                if all_area_path.contains_point(pos):
                    break
                pos = np.random.uniform(low=np.min(Config.ALL_AREA, axis=0), high=np.max(Config.ALL_AREA, axis=0))
            self.random_target_pos.append(pos)
        return self._get_obs()

    def step(self, action):
        # 解析动作
        self.intercepted = 0
        self.lock_success = 0
        self.be_locked = 0
        idx = 0
        for boat in self.white_boats:
            if boat.frozen_time > 0 or not boat.active:
                boat.frozen_time -= self.dt if boat.frozen_time > 0 else 0
                idx += 2
                continue
            target_speed = (action[idx] + 1) / 2 * boat.max_speed
            target_heading = action[idx + 1] * math.pi
            boat.update_position(self.dt, target_heading, target_speed)
            idx += 2

        for drone in self.white_drones:
            if drone.energy <= 0 or not drone.active:
                drone.active = False
                continue
            if drone.on_boat:
                # 起飞决策（简化：如果动作>0起飞）
                if action[idx] > 0 and drone.energy >= 1:
                    drone.on_boat = None
                    drone.charging = False
                    drone.vel = 0.04
                else:
                    drone.pos = drone.on_boat.pos.copy()
                    drone.update_energy(self.dt)
                    idx += 2
                    continue
            else:
                # 降落检查
                for boat in self.white_boats:
                    if np.linalg.norm(drone.pos - boat.pos) < 0.1 and drone.vel < 0.03:
                        if all(d.on_boat != boat for d in self.white_drones if d != drone):
                            drone.on_boat = boat
                            drone.charging = True
                            drone.vel = 0
                            break

            # 无人机智能跟踪逻辑
            detected_targets = drone.detect(self.black_boats)
            
            # 检查是否需要开始新的跟踪
            if not drone.tracking_mode and detected_targets:
                # 选择最近的目标开始跟踪
                closest_target = min(detected_targets, key=lambda t: np.linalg.norm(drone.pos - t.pos))
                drone.start_tracking(closest_target)
            
            # 更新跟踪行为
            tracking_heading, tracking_speed = drone.update_tracking(self.dt, detected_targets)
            
            if tracking_heading is not None and tracking_speed is not None:
                # 使用跟踪计算的目标朝向和速度
                target_heading = tracking_heading
                target_speed = tracking_speed
            else:
                # 使用原始动作控制
                target_speed = drone.min_speed + (action[idx] + 1) / 2 * (drone.max_speed - drone.min_speed)
                target_heading = action[idx + 1] * math.pi
                
                # 能量不足时自动返回母船
                if drone.energy < 0.2:  # 能量低于20%时返回
                    nearest_boat = min(self.white_boats, key=lambda b: np.linalg.norm(drone.pos - b.pos))
                    return_vec = nearest_boat.pos - drone.pos
                    target_heading = math.atan2(return_vec[1], return_vec[0])
                    target_speed = drone.max_speed

            drone.update_position(self.dt, target_heading, target_speed)
            drone.update_energy(self.dt)
            idx += 2

        # 更新黑方（简单策略：直线向A1A6中点移动）
        target_point = (Config.BREAKTHROUGH_LINE[0] + Config.BREAKTHROUGH_LINE[1]) / 2
        for boat in self.black_boats[:]:
            if boat.frozen_time > 0 or not boat.active:
                boat.frozen_time -= self.dt if boat.frozen_time > 0 else 0
                continue
            direction = target_point - boat.pos
            target_heading = math.atan2(direction[1], direction[0])
            boat.update_position(self.dt, target_heading, boat.max_speed)

            # 检查突防
            if self._cross_line(boat.pos, Config.BREAKTHROUGH_LINE):
                print('通过了！')
                self.black_boats.remove(boat)
                # 未拦截，负奖励

        # 探测和锁定（简化：每个白方艇尝试锁定最近黑方）
        detected_by_drones = []
        for drone in self.white_drones:
            detected_by_drones.extend(drone.detect(self.black_boats))
        for boat in self.white_boats:
            if boat.exited:
                for drone in self.white_drones[:]:
                    if drone.on_boat == boat:
                        drone.active = False
                boat.active = False
                continue
                # self.be_locked += 1
            # self.be_locked += boat.lock_count
            if boat.frozen_time > 0:
                continue
            detected = boat.detect(self.black_boats) + detected_by_drones  # 跨平台
            # if detected and not boat.lock_target:
            if detected:
                can_target = [t for t in detected if t.frozen_time <= 0]
                if can_target:
                    target = min(can_target, key=lambda t: np.linalg.norm(boat.pos - t.pos))
                    if boat.lock(target, self.dt):
                        self.lock_success += 1
                        self.total_lock_success += 1
                        if target.lock_count >= 2:
                            self.intercepted += 1
            

        # 黑方锁定白方（简化）
        for b_boat in self.black_boats:
            # print(b_boat.exited)
            if b_boat.exited:
                b_boat.active = False
                self.total_black_intercepted += 1
                continue
            if b_boat.frozen_time > 0:
                continue
            detected = b_boat.detect(self.white_boats)
            if detected:
                can_target = [t for t in detected if t.frozen_time <= 0]
                if can_target:
                    target = min(can_target, key=lambda t: np.linalg.norm(b_boat.pos - t.pos))
                    if b_boat.lock(target, self.dt):
                        self.be_locked += 1
                        self.total_be_locked += 1

        # 碰撞检测
        all_boats = [b for b in self.white_boats + self.black_boats if b.active]
        for i in range(len(all_boats)):
            for j in range(i + 1, len(all_boats)):
                if np.linalg.norm(all_boats[i].pos - all_boats[j].pos) < 0.1:
                    self.collisions += 1
                    self.total_collisions += 1

        # 边界检查：确保所有船只不得出任务区域
        self._enforce_boundary_constraints()

        self.time += self.dt

        obs = self._get_obs()
        reward = self.intercepted * 10 - self.be_locked * 5 - self.collisions * 2 + self.lock_success
        done = all(not b.active for b in self.black_boats) or self.time > 144000  # 1小时
        info = {}
        return obs, reward, done, info
#观测应当加上判断是无人艇还是无人机，是否是友方，无人机是否在艇上等
    def _get_obs(self):
        obs = []
        entities = self.white_boats + self.white_drones + self.black_boats
        for e in entities:
            if not e.active:
                obs.extend([0, 0, 0, 0, 0, 0])  # 假设不观测静止实体
            else:
                obs.extend([e.pos[0], e.pos[1], e.vel, e.heading, 1 if hasattr(e, 'energy') else e.lock_time, 1 if e.frozen_time > 0 else 0])
        return np.array(obs)

    def _is_point_in_polygon(self, point, polygon_vertices):
        """
        使用射线法判断点是否在多边形内部
        """
        x, y = point
        n = len(polygon_vertices)
        inside = False

        p1x, p1y = polygon_vertices[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon_vertices[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def _get_closest_point_on_boundary(self, point, polygon_vertices):
        """
        找到多边形边界上距离给定点最近的点
        """
        min_distance = float('inf')
        closest_point = point

        n = len(polygon_vertices)
        for i in range(n):
            p1 = polygon_vertices[i]
            p2 = polygon_vertices[(i + 1) % n]

            # 计算点到线段的最近点
            line_vec = p2 - p1
            point_vec = point - p1

            line_len_sq = np.dot(line_vec, line_vec)
            if line_len_sq == 0:
                # 线段退化为点
                candidate = p1
            else:
                t = max(0, min(1, np.dot(point_vec, line_vec) / line_len_sq))
                candidate = p1 + t * line_vec

            distance = np.linalg.norm(point - candidate)
            if distance < min_distance:
                min_distance = distance
                closest_point = candidate

        return closest_point

    def _enforce_boundary_constraints(self):
        """
        强制执行边界约束，将越界的船只拉回到边界内
        """
        # 任务区域的顶点（按顺序）
        area_vertices = [
            Config.AREA_POINTS['A1'],
            Config.AREA_POINTS['A2'],
            Config.AREA_POINTS['A3'],
            Config.AREA_POINTS['A4'],
            Config.AREA_POINTS['A5'],
            Config.AREA_POINTS['A6'],
            Config.AREA_POINTS['A7'],
            Config.AREA_POINTS['A8']
        ]

        # 检查所有白方船只
        for boat in self.white_boats:
            if not boat.active:
                continue
            if not self._is_point_in_polygon(boat.pos, area_vertices):
                # 船只越界，将其拉回到最近的边界点
                boat.pos = self._get_closest_point_on_boundary(boat.pos, area_vertices)
                # 可选：给予惩罚或重置速度
                boat.vel = 0  # 停止移动作为惩罚

        # 检查所有白方无人机
        for drone in self.white_drones:
            if not drone.active or drone.on_boat:
                continue
            if not self._is_point_in_polygon(drone.pos, area_vertices):
                # 无人机越界，将其拉回到最近的边界点
                drone.pos = self._get_closest_point_on_boundary(drone.pos, area_vertices)
                # 可选：给予惩罚
                drone.vel = drone.min_speed  # 降低到最小速度

        # 检查所有黑方船只
        for boat in self.black_boats:
            if not boat.active:
                continue
            if not self._is_point_in_polygon(boat.pos, area_vertices):
                # 黑方船只越界，将其拉回到最近的边界点
                boat.pos = self._get_closest_point_on_boundary(boat.pos, area_vertices)
                boat.vel = 0  # 停止移动作为惩罚

    def _cross_line(self, pos, line):
        # 简单检查是否跨越线（假设线是y=constant或类似）
        return pos[1] < Config.BREAKTHROUGH_LINE[0][1]  # 假设向上跨越

    def render(self, mode='human'):
        if self.screen is None:
            pygame.init()
            self.screen = pygame.display.set_mode((Config.SCREEN_WIDTH, Config.SCREEN_HEIGHT))

        self.screen.fill(Config.LIGHT_BLUE)

        # 缩放坐标
        def scale_pos(pos):
            return (int((pos[0] + Config.SCREEN_WIDTH / 2)), int(Config.SCREEN_HEIGHT - (pos[1] + Config.SCREEN_HEIGHT / 5)))

        # 绘制区域
        points = [scale_pos(Config.AREA_POINTS[p]) for p in ['A1', 'A2', 'A3', 'A4', 'A5', 'A6', 'A7', 'A8', 'A1']]
        pygame.draw.lines(self.screen, Config.BLACK, False, points, 2)

        # 绘制红线
        pygame.draw.line(self.screen, Config.RED, scale_pos(Config.BREAKTHROUGH_LINE[0]), scale_pos(Config.BREAKTHROUGH_LINE[1]), 3)

        arc_surface = pygame.Surface(self.screen.get_size(), pygame.SRCALPHA)
        # 绘制白方艇探测圆
        for boat in self.white_boats:
            if not boat.active:
                continue
            center = scale_pos(boat.pos)
            radius = int(boat.detect_range)
            pygame.draw.circle(arc_surface, (0, 0, 255, 50), center, radius)  # 半透明蓝圈
            
        # 绘制白方无人机扇形
        for drone in self.white_drones:
            if not drone.active:
                continue
            center = scale_pos(drone.pos)
            radius = int(drone.detect_range)
            start_angle = drone.heading - drone.detect_angle / 2
            end_angle = drone.heading + drone.detect_angle / 2
            points = [center]
            num_points = 20  # 弧线平滑度
            for i in range(num_points + 1):
                angle = start_angle + (end_angle - start_angle) * (i / num_points)
                x = center[0] + radius * math.cos(angle)
                y = center[1] - radius * math.sin(angle)
                points.append((x, y))
            pygame.draw.polygon(arc_surface, (0, 255, 0, 50), points)
            # pygame.draw.arc(self.screen, (0, 255, 0, 50), (center[0] - radius, center[1] - radius, 2 * radius, 2 * radius), start_angle, end_angle, 1)
        
        # 绘制黑方艇探测圆
        for boat in self.black_boats:
            if not boat.active:
                continue
            center = scale_pos(boat.pos)
            radius = int(boat.detect_range)
            pygame.draw.circle(arc_surface, (100, 0, 0, 50), center, radius)  # 半透明红圈
        self.screen.blit(arc_surface, (0, 0))

        # 绘制白方艇
        for boat in self.white_boats:
            if not boat.active:
                continue
            color = Config.WHITE if boat.lock_count == 0 else Config.YELLOW
            pygame.draw.circle(self.screen, color, scale_pos(boat.pos), 5)

        # 绘制白方无人机
        for drone in self.white_drones:
            if not drone.active:
                continue
            r = int(255 * (1-drone.energy))
            g = int(255 * drone.energy)
            color = (r, g, 0)
            
            # 跟踪模式时使用特殊颜色
            if drone.tracking_mode:
                color = (255, 165, 0)  # 橙色表示跟踪模式
                
            center = scale_pos(drone.pos)
            size = 6
            pygame.draw.rect(self.screen, color, (center[0] - size // 2, center[1] - size // 2, size, size))
            
            # 绘制跟踪连线
            if drone.tracking_mode and drone.tracking_target and drone.tracking_target.active:
                target_center = scale_pos(drone.tracking_target.pos)
                pygame.draw.line(self.screen, (255, 165, 0), center, target_center, 2)

        # 绘制黑方艇
        for boat in self.black_boats:
            if not boat.active:
                continue
            color = Config.BLACK if boat.lock_count == 0 else Config.RED
            pygame.draw.circle(self.screen, color, scale_pos(boat.pos), 4)

        
        pygame.display.flip()

    def close(self):
        if self.screen is not None:
            pygame.quit()
